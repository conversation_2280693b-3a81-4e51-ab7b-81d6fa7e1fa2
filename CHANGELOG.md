# 更新日志

## [2025-01-17] 小程序码功能

### 新增功能
- **活动小程序码生成**：为活动报名管理系统添加了小程序码生成功能
  - 支持为指定活动生成专属小程序码
  - 用户扫码可直接跳转到活动详情页面
  - 支持自定义小程序码参数（宽度、颜色、透明度等）

### 技术实现
- 基于微信小程序官方API `getwxacode` 接口
- 在 `WechatMiniappService` 中添加小程序码生成方法
- 在 `MiniEventController` 中添加相关接口
- 支持返回Base64编码的图片数据

### 新增接口
1. `POST /miniapp/event/getQRCode` - 获取活动小程序码
2. `POST /miniapp/event/getCustomQRCode` - 获取自定义小程序码

### 权限配置
- 新增权限标识：`miniapp:event:qrcode`
- 需要为相关用户分配此权限才能使用小程序码功能

### 配置要求
- 需要在 `application.yml` 中配置有效的微信小程序 `appid` 和 `secret`
- 确保微信小程序已发布并可正常访问

### 文档
- 添加了详细的API接口文档：`docs/小程序码接口文档.md`
- 包含接口说明、参数详情、使用示例等

### 测试
- 添加了基础的单元测试：`WechatMiniappServiceTest`
- 包含配置验证和小程序码生成测试

### 注意事项
- 小程序码生成有数量限制（与createQRCode共享100,000个）
- 建议合理使用，避免频繁调用
- 生成的小程序码永久有效
