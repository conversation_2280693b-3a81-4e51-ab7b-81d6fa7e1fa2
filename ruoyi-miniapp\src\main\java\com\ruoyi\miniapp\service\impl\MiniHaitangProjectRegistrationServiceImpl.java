package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniHaitangProjectRegistrationMapper;
import com.ruoyi.miniapp.domain.MiniHaitangProjectRegistration;
import com.ruoyi.miniapp.service.IMiniHaitangProjectRegistrationService;

/**
 * 天大海棠杯项目报名记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class MiniHaitangProjectRegistrationServiceImpl implements IMiniHaitangProjectRegistrationService 
{
    @Autowired
    private MiniHaitangProjectRegistrationMapper miniHaitangProjectRegistrationMapper;

    /**
     * 查询天大海棠杯项目报名记录
     *
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 天大海棠杯项目报名记录
     */
    @Override
    public MiniHaitangProjectRegistration selectMiniHaitangProjectRegistrationByRegistrationId(Long registrationId)
    {
        return miniHaitangProjectRegistrationMapper.selectMiniHaitangProjectRegistrationByRegistrationId(registrationId);
    }

    /**
     * 查询天大海棠杯项目报名记录（包含表单配置信息）
     *
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 天大海棠杯项目报名记录
     */
    @Override
    public MiniHaitangProjectRegistration selectMiniHaitangProjectRegistrationWithConfigByRegistrationId(Long registrationId)
    {
        return miniHaitangProjectRegistrationMapper.selectMiniHaitangProjectRegistrationWithConfigByRegistrationId(registrationId);
    }

    /**
     * 查询天大海棠杯项目报名记录列表
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 天大海棠杯项目报名记录
     */
    @Override
    public List<MiniHaitangProjectRegistration> selectMiniHaitangProjectRegistrationList(MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        return miniHaitangProjectRegistrationMapper.selectMiniHaitangProjectRegistrationList(miniHaitangProjectRegistration);
    }

    /**
     * 查询天大海棠杯项目报名记录列表（关联表单配置名称）
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 天大海棠杯项目报名记录
     */
    @Override
    public List<MiniHaitangProjectRegistration> selectMiniHaitangProjectRegistrationListWithConfig(MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        return miniHaitangProjectRegistrationMapper.selectMiniHaitangProjectRegistrationListWithConfig(miniHaitangProjectRegistration);
    }

    /**
     * 新增天大海棠杯项目报名记录
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    @Override
    public int insertMiniHaitangProjectRegistration(MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        miniHaitangProjectRegistration.setCreateTime(DateUtils.getNowDate());
        if (miniHaitangProjectRegistration.getRegistrationTime() == null) {
            miniHaitangProjectRegistration.setRegistrationTime(DateUtils.getNowDate());
        }
        return miniHaitangProjectRegistrationMapper.insertMiniHaitangProjectRegistration(miniHaitangProjectRegistration);
    }

    /**
     * 修改天大海棠杯项目报名记录
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    @Override
    public int updateMiniHaitangProjectRegistration(MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        miniHaitangProjectRegistration.setUpdateTime(DateUtils.getNowDate());
        return miniHaitangProjectRegistrationMapper.updateMiniHaitangProjectRegistration(miniHaitangProjectRegistration);
    }

    /**
     * 批量删除天大海棠杯项目报名记录
     * 
     * @param registrationIds 需要删除的天大海棠杯项目报名记录主键
     * @return 结果
     */
    @Override
    public int deleteMiniHaitangProjectRegistrationByRegistrationIds(Long[] registrationIds)
    {
        return miniHaitangProjectRegistrationMapper.deleteMiniHaitangProjectRegistrationByRegistrationIds(registrationIds);
    }

    /**
     * 删除天大海棠杯项目报名记录信息
     * 
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 结果
     */
    @Override
    public int deleteMiniHaitangProjectRegistrationByRegistrationId(Long registrationId)
    {
        return miniHaitangProjectRegistrationMapper.deleteMiniHaitangProjectRegistrationByRegistrationId(registrationId);
    }


}
