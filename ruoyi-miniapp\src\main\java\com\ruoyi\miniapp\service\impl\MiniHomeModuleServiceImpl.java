package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniHomeModuleMapper;
import com.ruoyi.miniapp.domain.MiniHomeModule;
import com.ruoyi.miniapp.service.IMiniHomeModuleService;

/**
 * 小程序首页功能模块Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class MiniHomeModuleServiceImpl implements IMiniHomeModuleService 
{
    @Autowired
    private MiniHomeModuleMapper miniHomeModuleMapper;

    /**
     * 查询小程序首页功能模块
     * 
     * @param id 小程序首页功能模块主键
     * @return 小程序首页功能模块
     */
    @Override
    public MiniHomeModule selectMiniHomeModuleById(Long id)
    {
        return miniHomeModuleMapper.selectMiniHomeModuleById(id);
    }

    /**
     * 查询小程序首页功能模块列表
     * 
     * @param miniHomeModule 小程序首页功能模块
     * @return 小程序首页功能模块
     */
    @Override
    public List<MiniHomeModule> selectMiniHomeModuleList(MiniHomeModule miniHomeModule)
    {
        return miniHomeModuleMapper.selectMiniHomeModuleList(miniHomeModule);
    }

    /**
     * 新增小程序首页功能模块
     * 
     * @param miniHomeModule 小程序首页功能模块
     * @return 结果
     */
    @Override
    public int insertMiniHomeModule(MiniHomeModule miniHomeModule)
    {
        miniHomeModule.setCreateTime(DateUtils.getNowDate());
        return miniHomeModuleMapper.insertMiniHomeModule(miniHomeModule);
    }

    /**
     * 修改小程序首页功能模块
     * 
     * @param miniHomeModule 小程序首页功能模块
     * @return 结果
     */
    @Override
    public int updateMiniHomeModule(MiniHomeModule miniHomeModule)
    {
        miniHomeModule.setUpdateTime(DateUtils.getNowDate());
        return miniHomeModuleMapper.updateMiniHomeModule(miniHomeModule);
    }

    /**
     * 批量删除小程序首页功能模块
     * 
     * @param ids 需要删除的小程序首页功能模块主键
     * @return 结果
     */
    @Override
    public int deleteMiniHomeModuleByIds(Long[] ids)
    {
        return miniHomeModuleMapper.deleteMiniHomeModuleByIds(ids);
    }

    /**
     * 删除小程序首页功能模块信息
     * 
     * @param id 小程序首页功能模块主键
     * @return 结果
     */
    @Override
    public int deleteMiniHomeModuleById(Long id)
    {
        return miniHomeModuleMapper.deleteMiniHomeModuleById(id);
    }

    /**
     * 根据模块代码查询小程序首页功能模块
     *
     * @param moduleCode 模块代码
     * @return 小程序首页功能模块
     */
    @Override
    public MiniHomeModule selectMiniHomeModuleByModuleCode(String moduleCode)
    {
        return miniHomeModuleMapper.selectMiniHomeModuleByModuleCode(moduleCode);
    }

    /**
     * 校验模块代码是否唯一
     * 
     * @param miniHomeModule 小程序首页功能模块
     * @return 结果
     */
    @Override
    public boolean checkModuleCodeUnique(MiniHomeModule miniHomeModule)
    {
        Long id = StringUtils.isNull(miniHomeModule.getId()) ? -1L : miniHomeModule.getId();
        MiniHomeModule info = miniHomeModuleMapper.selectMiniHomeModuleByModuleCode(miniHomeModule.getModuleCode());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 查询正常状态的模块列表（小程序端使用）
     *
     * @return 小程序首页功能模块集合
     */
    @Override
    public List<MiniHomeModule> selectActiveModuleList()
    {
        return miniHomeModuleMapper.selectActiveModuleList();
    }
}
