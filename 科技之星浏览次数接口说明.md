# 科技之星浏览次数加一接口

## 接口信息

**接口地址**: `POST /miniapp/techstar/incrementViewCount/{starId}`

**接口描述**: 增加指定科技之星的浏览次数

**请求方式**: POST

**权限要求**: 无需权限验证（小程序端调用）

## 请求参数

| 参数名 | 参数类型 | 是否必填 | 参数描述 |
|--------|----------|----------|----------|
| starId | Long | 是 | 科技之星ID，通过路径参数传递 |

## 请求示例

```http
POST /miniapp/techstar/incrementViewCount/1
```

## 响应参数

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 业务逻辑

1. **自动处理空值**: 如果 `view_count` 字段为 `null`，会自动设置为 0 再加 1
2. **原子操作**: 使用数据库的原子更新操作，确保并发安全
3. **更新时间**: 同时更新 `update_time` 字段为当前时间
4. **返回结果**: 返回影响的行数（成功为1，失败为0）

## 前端调用示例

### JavaScript/Vue.js

```javascript
import { incrementViewCount } from "@/api/miniapp/techstar";

// 增加浏览次数
incrementViewCount(starId).then(response => {
  if (response.code === 200) {
    console.log('浏览次数增加成功');
  }
}).catch(error => {
  console.error('增加浏览次数失败:', error);
});
```

### 小程序端调用

```javascript
// 在小程序中调用
wx.request({
  url: 'https://your-domain.com/miniapp/techstar/incrementViewCount/1',
  method: 'POST',
  success: function(res) {
    if (res.data.code === 200) {
      console.log('浏览次数增加成功');
    }
  },
  fail: function(error) {
    console.error('增加浏览次数失败:', error);
  }
});
```

## 使用场景

1. **详情页访问**: 用户点击查看科技之星详情时调用
2. **列表点击**: 用户在列表中点击某个科技之星时调用
3. **分享统计**: 通过分享链接访问时调用
4. **搜索结果**: 从搜索结果点击进入时调用

## 数据库变更

### SQL实现
```sql
UPDATE mini_tech_star 
SET view_count = IFNULL(view_count, 0) + 1,
    update_time = now()
WHERE star_id = #{starId}
```

### 字段说明
- `view_count`: 浏览次数字段，类型为 `int`，可为空
- `update_time`: 更新时间字段，记录最后一次操作时间

## 代码文件变更

### 后端文件
1. **Controller**: `MiniTechStarController.java` - 添加接口方法
2. **Service接口**: `IMiniTechStarService.java` - 添加服务方法
3. **Service实现**: `MiniTechStarServiceImpl.java` - 添加业务逻辑
4. **Mapper接口**: `MiniTechStarMapper.java` - 添加数据访问方法
5. **Mapper.xml**: `MiniTechStarMapper.xml` - 添加SQL实现

### 前端文件
1. **API文件**: `techstar.js` - 添加前端调用方法

## 测试验证

### 测试步骤
1. 调用接口前查询浏览次数
2. 调用浏览次数增加接口
3. 再次查询验证浏览次数是否增加

### 测试结果
```sql
-- 调用前
SELECT view_count FROM mini_tech_star WHERE star_id = 1;
-- 结果: 0

-- 调用接口后
SELECT view_count FROM mini_tech_star WHERE star_id = 1;
-- 结果: 1
```

## 注意事项

1. **并发安全**: 使用数据库原子操作，支持高并发访问
2. **空值处理**: 自动处理 `view_count` 为空的情况
3. **错误处理**: 如果 `starId` 不存在，返回影响行数为0
4. **性能考虑**: 单次更新操作，性能良好
5. **数据一致性**: 同时更新浏览次数和修改时间，保证数据一致性

## API文档更新

建议在API文档中添加此接口的详细说明，方便前端和小程序端开发者使用。
