package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniRoadshowManagement;
import com.ruoyi.miniapp.service.IMiniRoadshowManagementService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 创赛路演管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Api(tags = "创赛路演管理")
@RestController
@RequestMapping("/miniapp/roadshow")
public class MiniRoadshowManagementController extends BaseController
{
    @Autowired
    private IMiniRoadshowManagementService miniRoadshowManagementService;

    /**
     * 查询创赛路演管理列表
     */
    @ApiOperation("查询创赛路演管理列表")
    @PreAuthorize("@ss.hasPermi('config:roadshow:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniRoadshowManagement miniRoadshowManagement)
    {
        startPage();
        List<MiniRoadshowManagement> list = miniRoadshowManagementService.selectMiniRoadshowManagementList(miniRoadshowManagement);
        return getDataTable(list);
    }

    /**
     * 导出创赛路演管理列表
     */
    @ApiOperation("导出创赛路演管理列表")
    @PreAuthorize("@ss.hasPermi('config:roadshow:export')")
    @Log(title = "创赛路演管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniRoadshowManagement miniRoadshowManagement)
    {
        List<MiniRoadshowManagement> list = miniRoadshowManagementService.selectMiniRoadshowManagementList(miniRoadshowManagement);
        ExcelUtil<MiniRoadshowManagement> util = new ExcelUtil<MiniRoadshowManagement>(MiniRoadshowManagement.class);
        util.exportExcel(response, list, "创赛路演管理数据");
    }

    /**
     * 获取创赛路演管理详细信息
     */
    @ApiOperation("获取创赛路演管理详细信息")
    @PreAuthorize("@ss.hasPermi('config:roadshow:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("创赛路演管理ID") @PathVariable("id") Long id)
    {
        return AjaxResult.success(miniRoadshowManagementService.selectMiniRoadshowManagementById(id));
    }

    /**
     * 新增创赛路演管理
     */
    @ApiOperation("新增创赛路演管理")
    @PreAuthorize("@ss.hasPermi('config:roadshow:add')")
    @Log(title = "创赛路演管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("创赛路演管理") @RequestBody MiniRoadshowManagement miniRoadshowManagement)
    {
        if (!miniRoadshowManagementService.checkUniqueCodeUnique(miniRoadshowManagement))
        {
            return AjaxResult.error("新增创赛路演'" + miniRoadshowManagement.getTitle() + "'失败，唯一标识已存在");
        }
        miniRoadshowManagement.setCreateBy(getUsername());
        return toAjax(miniRoadshowManagementService.insertMiniRoadshowManagement(miniRoadshowManagement));
    }

    /**
     * 修改创赛路演管理
     */
    @ApiOperation("修改创赛路演管理")
    @PreAuthorize("@ss.hasPermi('config:roadshow:edit')")
    @Log(title = "创赛路演管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("创赛路演管理") @RequestBody MiniRoadshowManagement miniRoadshowManagement)
    {
        // 唯一标识不允许修改，从数据库获取原有的唯一标识
        MiniRoadshowManagement existingRoadshow = miniRoadshowManagementService.selectMiniRoadshowManagementById(miniRoadshowManagement.getId());
        if (existingRoadshow != null) {
            miniRoadshowManagement.setUniqueCode(existingRoadshow.getUniqueCode());
        }
        miniRoadshowManagement.setUpdateBy(getUsername());
        return toAjax(miniRoadshowManagementService.updateMiniRoadshowManagement(miniRoadshowManagement));
    }

    /**
     * 删除创赛路演管理
     */
    @ApiOperation("删除创赛路演管理")
    @PreAuthorize("@ss.hasPermi('config:roadshow:remove')")
    @Log(title = "创赛路演管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("创赛路演管理ID数组") @PathVariable Long[] ids)
    {
        return toAjax(miniRoadshowManagementService.deleteMiniRoadshowManagementByIds(ids));
    }

    /**
     * 获取启用的创赛路演管理列表
     */
    @ApiOperation("获取启用的创赛路演管理列表")
    @GetMapping("/enabled")
    public AjaxResult getEnabledList()
    {
        List<MiniRoadshowManagement> list = miniRoadshowManagementService.selectEnabledMiniRoadshowManagementList();
        return AjaxResult.success(list);
    }

    /**
     * 校验唯一标识
     */
    @ApiOperation("校验唯一标识")
    @PostMapping("/checkUniqueCodeUnique")
    public AjaxResult checkUniqueCodeUnique(@ApiParam("创赛路演管理") @RequestBody MiniRoadshowManagement miniRoadshowManagement)
    {
        return AjaxResult.success(miniRoadshowManagementService.checkUniqueCodeUnique(miniRoadshowManagement));
    }

    // ==================== 小程序端接口 ====================

    /**
     * 根据唯一标识获取创赛路演详情（小程序端）
     */
    @ApiOperation("根据唯一标识获取创赛路演详情")
    @GetMapping("/app/getByCode/{uniqueCode}")
    public AjaxResult getByUniqueCode(@ApiParam("唯一标识") @PathVariable("uniqueCode") String uniqueCode)
    {
        MiniRoadshowManagement roadshow = miniRoadshowManagementService.selectMiniRoadshowManagementByUniqueCode(uniqueCode);
        if (roadshow == null || !"0".equals(roadshow.getStatus())) {
            return AjaxResult.error("创赛路演不存在或已停用");
        }
        return AjaxResult.success(roadshow);
    }

    /**
     * 获取启用的创赛路演列表（小程序端）
     */
    @ApiOperation("获取启用的创赛路演列表")
    @GetMapping("/app/getEnabledList")
    public AjaxResult getEnabledListForApp()
    {
        List<MiniRoadshowManagement> list = miniRoadshowManagementService.selectEnabledMiniRoadshowManagementList();
        return AjaxResult.success(list);
    }
}
