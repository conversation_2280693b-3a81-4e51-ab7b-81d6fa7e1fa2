package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniProjectInvestmentMapper;
import com.ruoyi.miniapp.domain.MiniProjectInvestment;
import com.ruoyi.miniapp.service.IMiniProjectInvestmentService;

/**
 * 项目投资Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class MiniProjectInvestmentServiceImpl implements IMiniProjectInvestmentService 
{
    @Autowired
    private MiniProjectInvestmentMapper miniProjectInvestmentMapper;

    /**
     * 查询项目投资
     * 
     * @param investmentId 项目投资主键
     * @return 项目投资
     */
    @Override
    public MiniProjectInvestment selectMiniProjectInvestmentByInvestmentId(Long investmentId)
    {
        return miniProjectInvestmentMapper.selectMiniProjectInvestmentByInvestmentId(investmentId);
    }

    /**
     * 查询项目投资列表
     * 
     * @param miniProjectInvestment 项目投资
     * @return 项目投资
     */
    @Override
    public List<MiniProjectInvestment> selectMiniProjectInvestmentList(MiniProjectInvestment miniProjectInvestment)
    {
        return miniProjectInvestmentMapper.selectMiniProjectInvestmentList(miniProjectInvestment);
    }

    /**
     * 新增项目投资
     * 
     * @param miniProjectInvestment 项目投资
     * @return 结果
     */
    @Override
    public int insertMiniProjectInvestment(MiniProjectInvestment miniProjectInvestment)
    {
        miniProjectInvestment.setCreateTime(DateUtils.getNowDate());
        return miniProjectInvestmentMapper.insertMiniProjectInvestment(miniProjectInvestment);
    }

    /**
     * 修改项目投资
     * 
     * @param miniProjectInvestment 项目投资
     * @return 结果
     */
    @Override
    public int updateMiniProjectInvestment(MiniProjectInvestment miniProjectInvestment)
    {
        miniProjectInvestment.setUpdateTime(DateUtils.getNowDate());
        return miniProjectInvestmentMapper.updateMiniProjectInvestment(miniProjectInvestment);
    }

    /**
     * 批量删除项目投资
     * 
     * @param investmentIds 需要删除的项目投资主键
     * @return 结果
     */
    @Override
    public int deleteMiniProjectInvestmentByInvestmentIds(Long[] investmentIds)
    {
        return miniProjectInvestmentMapper.deleteMiniProjectInvestmentByInvestmentIds(investmentIds);
    }

    /**
     * 删除项目投资信息
     * 
     * @param investmentId 项目投资主键
     * @return 结果
     */
    @Override
    public int deleteMiniProjectInvestmentByInvestmentId(Long investmentId)
    {
        return miniProjectInvestmentMapper.deleteMiniProjectInvestmentByInvestmentId(investmentId);
    }

    /**
     * 查询启用的项目投资列表（小程序端调用）
     *
     * @return 项目投资集合
     */
    @Override
    public List<MiniProjectInvestment> selectEnabledMiniProjectInvestmentList()
    {
        return miniProjectInvestmentMapper.selectEnabledMiniProjectInvestmentList();
    }

    /**
     * 查询启用的项目投资列表（带筛选条件）
     *
     * @param miniProjectInvestment 筛选条件
     * @param keyword 关键字
     * @return 项目投资集合
     */
    @Override
    public List<MiniProjectInvestment> selectEnabledMiniProjectInvestmentListWithFilter(MiniProjectInvestment miniProjectInvestment, String keyword)
    {
        return miniProjectInvestmentMapper.selectEnabledMiniProjectInvestmentListWithFilter(miniProjectInvestment, keyword);
    }

    /**
     * 查询推荐的项目投资列表
     *
     * @return 项目投资集合
     */
    @Override
    public List<MiniProjectInvestment> selectRecommendedMiniProjectInvestmentList()
    {
        return miniProjectInvestmentMapper.selectRecommendedMiniProjectInvestmentList();
    }

    /**
     * 增加项目投资浏览次数
     *
     * @param investmentId 项目投资主键
     * @return 结果
     */
    @Override
    public int incrementViewCount(Long investmentId)
    {
        return miniProjectInvestmentMapper.incrementViewCount(investmentId);
    }

    /**
     * 根据行业ID查询项目投资列表
     *
     * @param industryId 行业ID
     * @return 项目投资集合
     */
    @Override
    public List<MiniProjectInvestment> selectMiniProjectInvestmentByIndustryId(Long industryId)
    {
        return miniProjectInvestmentMapper.selectMiniProjectInvestmentByIndustryId(industryId);
    }

    /**
     * 根据融资轮次查询项目投资列表
     *
     * @param financingRound 融资轮次
     * @return 项目投资集合
     */
    @Override
    public List<MiniProjectInvestment> selectMiniProjectInvestmentByFinancingRound(String financingRound)
    {
        return miniProjectInvestmentMapper.selectMiniProjectInvestmentByFinancingRound(financingRound);
    }

    /**
     * 根据地区查询项目投资列表
     *
     * @param region 地区
     * @return 项目投资集合
     */
    @Override
    public List<MiniProjectInvestment> selectMiniProjectInvestmentByRegion(String region)
    {
        return miniProjectInvestmentMapper.selectMiniProjectInvestmentByRegion(region);
    }
}
