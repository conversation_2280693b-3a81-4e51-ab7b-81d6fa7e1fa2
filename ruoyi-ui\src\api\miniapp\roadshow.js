import request from '@/utils/request'

// 查询创赛路演管理列表
export function listRoadshow(query) {
  return request({
    url: '/miniapp/roadshow/list',
    method: 'get',
    params: query
  })
}

// 查询创赛路演管理详细
export function getRoadshow(id) {
  return request({
    url: '/miniapp/roadshow/' + id,
    method: 'get'
  })
}

// 新增创赛路演管理
export function addRoadshow(data) {
  return request({
    url: '/miniapp/roadshow',
    method: 'post',
    data: data
  })
}

// 修改创赛路演管理
export function updateRoadshow(data) {
  return request({
    url: '/miniapp/roadshow',
    method: 'put',
    data: data
  })
}

// 删除创赛路演管理
export function delRoadshow(id) {
  return request({
    url: '/miniapp/roadshow/' + id,
    method: 'delete'
  })
}

// 获取启用的创赛路演管理列表
export function getEnabledRoadshowList() {
  return request({
    url: '/miniapp/roadshow/enabled',
    method: 'get'
  })
}

// 校验唯一标识
export function checkUniqueCodeUnique(data) {
  return request({
    url: '/miniapp/roadshow/checkUniqueCodeUnique',
    method: 'post',
    data: data
  })
}

// 根据唯一标识获取创赛路演详情（小程序端）
export function getRoadshowByCode(uniqueCode) {
  return request({
    url: '/miniapp/roadshow/app/getByCode/' + uniqueCode,
    method: 'get'
  })
}

// 获取启用的创赛路演列表（小程序端）
export function getEnabledRoadshowListForApp() {
  return request({
    url: '/miniapp/roadshow/app/getEnabledList',
    method: 'get'
  })
}
