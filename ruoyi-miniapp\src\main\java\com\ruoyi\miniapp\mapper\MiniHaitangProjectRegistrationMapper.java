package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniHaitangProjectRegistration;
import org.apache.ibatis.annotations.Mapper;

/**
 * 天大海棠杯项目报名记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Mapper
public interface MiniHaitangProjectRegistrationMapper 
{
    /**
     * 查询天大海棠杯项目报名记录
     *
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 天大海棠杯项目报名记录
     */
    public MiniHaitangProjectRegistration selectMiniHaitangProjectRegistrationByRegistrationId(Long registrationId);

    /**
     * 查询天大海棠杯项目报名记录（包含表单配置信息）
     *
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 天大海棠杯项目报名记录
     */
    public MiniHaitangProjectRegistration selectMiniHaitangProjectRegistrationWithConfigByRegistrationId(Long registrationId);

    /**
     * 查询天大海棠杯项目报名记录列表
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 天大海棠杯项目报名记录集合
     */
    public List<MiniHaitangProjectRegistration> selectMiniHaitangProjectRegistrationList(MiniHaitangProjectRegistration miniHaitangProjectRegistration);

    /**
     * 查询天大海棠杯项目报名记录列表（关联表单配置名称）
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 天大海棠杯项目报名记录集合
     */
    public List<MiniHaitangProjectRegistration> selectMiniHaitangProjectRegistrationListWithConfig(MiniHaitangProjectRegistration miniHaitangProjectRegistration);

    /**
     * 新增天大海棠杯项目报名记录
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    public int insertMiniHaitangProjectRegistration(MiniHaitangProjectRegistration miniHaitangProjectRegistration);

    /**
     * 修改天大海棠杯项目报名记录
     * 
     * @param miniHaitangProjectRegistration 天大海棠杯项目报名记录
     * @return 结果
     */
    public int updateMiniHaitangProjectRegistration(MiniHaitangProjectRegistration miniHaitangProjectRegistration);

    /**
     * 删除天大海棠杯项目报名记录
     * 
     * @param registrationId 天大海棠杯项目报名记录主键
     * @return 结果
     */
    public int deleteMiniHaitangProjectRegistrationByRegistrationId(Long registrationId);

    /**
     * 批量删除天大海棠杯项目报名记录
     * 
     * @param registrationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniHaitangProjectRegistrationByRegistrationIds(Long[] registrationIds);



    /**
     * 根据配置ID查询报名记录数量
     * 
     * @param configId 配置ID
     * @return 报名记录数量
     */
    public int countByConfigId(Long configId);
}
