<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniHaitangProjectRegistrationMapper">
    
    <resultMap type="MiniHaitangProjectRegistration" id="MiniHaitangProjectRegistrationResult">
        <result property="registrationId"    column="registration_id"    />
        <result property="configId"    column="config_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="formData"    column="form_data"    />
        <result property="registrationTime"    column="registration_time"    />

        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="configName"    column="config_name"    />
    </resultMap>

    <sql id="selectMiniHaitangProjectRegistrationVo">
        select registration_id, config_id, user_id, user_name, user_phone, form_data, registration_time, create_by, create_time, update_by, update_time, remark from mini_haitang_project_registration
    </sql>

    <select id="selectMiniHaitangProjectRegistrationList" parameterType="MiniHaitangProjectRegistration" resultMap="MiniHaitangProjectRegistrationResult">
        <include refid="selectMiniHaitangProjectRegistrationVo"/>
        <where>  
            <if test="configId != null "> and config_id = #{configId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="userPhone != null  and userPhone != ''"> and user_phone like concat('%', #{userPhone}, '%')</if>

        </where>
        order by registration_time desc
    </select>

    <select id="selectMiniHaitangProjectRegistrationListWithConfig" parameterType="MiniHaitangProjectRegistration" resultMap="MiniHaitangProjectRegistrationResult">
        select r.registration_id, r.config_id, r.user_id, r.user_name, r.user_phone, r.form_data, r.registration_time, r.create_by, r.create_time, r.update_by, r.update_time, r.remark, c.config_name
        from mini_haitang_project_registration r
        left join mini_haitang_project_form_config c on r.config_id = c.config_id
        <where>
            <if test="configId != null "> and r.config_id = #{configId}</if>
            <if test="userId != null "> and r.user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and r.user_name like concat('%', #{userName}, '%')</if>
            <if test="userPhone != null  and userPhone != ''"> and r.user_phone like concat('%', #{userPhone}, '%')</if>
        </where>
        order by r.registration_time desc
    </select>
    
    <select id="selectMiniHaitangProjectRegistrationByRegistrationId" parameterType="Long" resultMap="MiniHaitangProjectRegistrationResult">
        <include refid="selectMiniHaitangProjectRegistrationVo"/>
        where registration_id = #{registrationId}
    </select>

    <select id="selectMiniHaitangProjectRegistrationWithConfigByRegistrationId" parameterType="Long" resultMap="MiniHaitangProjectRegistrationResult">
        select r.registration_id, r.config_id, r.user_id, r.user_name, r.user_phone, r.form_data, r.registration_time, r.create_by, r.create_time, r.update_by, r.update_time, r.remark, c.config_name, c.form_config as config_data
        from mini_haitang_project_registration r
        left join mini_haitang_project_form_config c on r.config_id = c.config_id
        where r.registration_id = #{registrationId}
    </select>

    <select id="countByConfigId" parameterType="Long" resultType="int">
        select count(*) from mini_haitang_project_registration where config_id = #{configId}
    </select>
        
    <insert id="insertMiniHaitangProjectRegistration" parameterType="MiniHaitangProjectRegistration" useGeneratedKeys="true" keyProperty="registrationId">
        insert into mini_haitang_project_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configId != null">config_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="userPhone != null">user_phone,</if>
            <if test="formData != null">form_data,</if>
            <if test="registrationTime != null">registration_time,</if>

            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configId != null">#{configId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="userPhone != null">#{userPhone},</if>
            <if test="formData != null">#{formData},</if>
            <if test="registrationTime != null">#{registrationTime},</if>

            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniHaitangProjectRegistration" parameterType="MiniHaitangProjectRegistration">
        update mini_haitang_project_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="configId != null">config_id = #{configId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="userPhone != null">user_phone = #{userPhone},</if>
            <if test="formData != null">form_data = #{formData},</if>
            <if test="registrationTime != null">registration_time = #{registrationTime},</if>

            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where registration_id = #{registrationId}
    </update>



    <delete id="deleteMiniHaitangProjectRegistrationByRegistrationId" parameterType="Long">
        delete from mini_haitang_project_registration where registration_id = #{registrationId}
    </delete>

    <delete id="deleteMiniHaitangProjectRegistrationByRegistrationIds" parameterType="String">
        delete from mini_haitang_project_registration where registration_id in 
        <foreach item="registrationId" collection="array" open="(" separator="," close=")">
            #{registrationId}
        </foreach>
    </delete>

</mapper>
