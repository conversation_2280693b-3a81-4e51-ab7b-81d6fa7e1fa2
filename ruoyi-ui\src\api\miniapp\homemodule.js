import request from '@/utils/request'

// 查询小程序首页功能模块列表
export function listHomeModule(query) {
  return request({
    url: '/miniapp/homemodule/list',
    method: 'get',
    params: query
  })
}

// 查询小程序首页功能模块详细
export function getHomeModule(id) {
  return request({
    url: '/miniapp/homemodule/' + id,
    method: 'get'
  })
}

// 新增小程序首页功能模块
export function addHomeModule(data) {
  return request({
    url: '/miniapp/homemodule',
    method: 'post',
    data: data
  })
}

// 修改小程序首页功能模块
export function updateHomeModule(data) {
  return request({
    url: '/miniapp/homemodule',
    method: 'put',
    data: data
  })
}

// 删除小程序首页功能模块
export function delHomeModule(id) {
  return request({
    url: '/miniapp/homemodule/' + id,
    method: 'delete'
  })
}

// 校验模块代码
export function checkModuleCodeUnique(data) {
  return request({
    url: '/miniapp/homemodule/checkModuleCodeUnique',
    method: 'post',
    data: data
  })
}

// 获取小程序首页功能模块列表（小程序端）
export function getModuleListForApp() {
  return request({
    url: '/miniapp/homemodule/app/getModuleList',
    method: 'get'
  })
}

// 根据模块代码获取模块详情（小程序端）
export function getHomeModuleByCode(moduleCode) {
  return request({
    url: '/miniapp/homemodule/app/getByCode/' + moduleCode,
    method: 'get'
  })
}
