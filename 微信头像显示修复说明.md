# 微信头像显示修复说明

## 问题描述

微信头像在前端页面中无法正常显示，主要原因是微信图片服务器的跨域限制和防盗链机制。

## 解决方案

使用 `<img referrerpolicy="no-referrer" />` 标签替代原有的 `el-avatar` 组件来显示微信头像。

### 核心修改

#### 1. 用户列表页面的微信头像显示

**修改位置**: `ruoyi-ui/src/views/miniapp/user/index.vue` 第172-177行

**修改前**:
```vue
<el-table-column label="微信头像" align="center" key="weixinAvatar" v-if="columns[2].visible" width="80">
  <template slot-scope="scope">
    <el-avatar v-if="scope.row.weixinAvatar" :src="scope.row.weixinAvatar" :size="40"></el-avatar>
    <el-avatar v-else :size="40" icon="el-icon-user-solid"></el-avatar>
  </template>
</el-table-column>
```

**修改后**:
```vue
<el-table-column label="微信头像" align="center" key="weixinAvatar" v-if="columns[2].visible" width="80">
  <template slot-scope="scope">
    <img v-if="scope.row.weixinAvatar" :src="scope.row.weixinAvatar" alt="" referrerpolicy="no-referrer" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;" />
    <el-avatar v-else :size="40" icon="el-icon-user-solid"></el-avatar>
  </template>
</el-table-column>
```

#### 2. 用户详情页面的微信头像显示

**修改位置**: `ruoyi-ui/src/views/miniapp/user/index.vue` 第285-291行

**修改前**:
```vue
<div class="avatar-item">
  <div class="avatar-label">微信头像</div>
  <div class="avatar-content">
    <image-preview :src="viewForm.weixinAvatar" :width="80" :height="80" v-if="viewForm.weixinAvatar"/>
    <div v-else class="no-avatar">未设置</div>
  </div>
</div>
```

**修改后**:
```vue
<div class="avatar-item">
  <div class="avatar-label">微信头像</div>
  <div class="avatar-content">
    <img v-if="viewForm.weixinAvatar" :src="viewForm.weixinAvatar" alt="" referrerpolicy="no-referrer" style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer;" @click="previewWeixinAvatar(viewForm.weixinAvatar)" />
    <div v-else class="no-avatar">未设置</div>
  </div>
</div>
```

#### 3. 新增微信头像预览功能

**新增方法**: `previewWeixinAvatar(avatarUrl)`

```javascript
/** 预览微信头像 */
previewWeixinAvatar(avatarUrl) {
  if (avatarUrl) {
    // 先显示加载中的对话框
    const loadingHtml = `
      <div style="text-align: center; padding: 20px;">
        <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
        <div style="margin-top: 10px; color: #666;">头像加载中...</div>
      </div>
    `;

    this.$msgbox({
      title: '微信头像预览',
      dangerouslyUseHTMLString: true,
      message: loadingHtml,
      showCancelButton: false,
      showConfirmButton: true,
      confirmButtonText: '关闭',
      customClass: 'avatar-preview-dialog'
    });

    // 预加载图片
    const img = new Image();
    img.onload = () => {
      // 图片加载成功后更新对话框内容
      const imgHtml = `
        <img
          src="${avatarUrl}"
          alt="微信头像预览"
          referrerpolicy="no-referrer"
          style="max-width: 100%; max-height: 400px; object-fit: contain; display: block; margin: 0 auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);"
        />
      `;

      // 更新对话框内容
      const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');
      if (messageBox) {
        messageBox.innerHTML = imgHtml;
      }
    };

    img.onerror = () => {
      // 图片加载失败处理
      const errorHtml = `
        <div style="text-align: center; padding: 20px; color: #F56C6C;">
          <i class="el-icon-picture-outline" style="font-size: 48px; margin-bottom: 10px;"></i>
          <div>头像加载失败</div>
          <div style="font-size: 12px; margin-top: 5px; color: #999;">请检查网络连接或图片链接</div>
        </div>
      `;

      const messageBox = document.querySelector('.avatar-preview-dialog .el-message-box__message');
      if (messageBox) {
        messageBox.innerHTML = errorHtml;
      }
    };

    img.src = avatarUrl;
    img.referrerPolicy = "no-referrer";
  }
}
```

## 技术原理

### `referrerpolicy="no-referrer"` 的作用

1. **防止防盗链检测**: 微信图片服务器会检查请求的 Referer 头，如果不是来自微信域名，会拒绝访问
2. **跨域访问**: 通过不发送 Referer 头，绕过微信的防盗链机制
3. **隐私保护**: 不向目标服务器泄露当前页面的 URL 信息

### CSS 样式说明

- `border-radius: 50%`: 列表中的头像显示为圆形
- `border-radius: 8px`: 详情页中的头像显示为圆角矩形
- `object-fit: cover`: 保持图片比例，裁剪多余部分
- `cursor: pointer`: 详情页头像可点击预览

## 功能特点

1. **兼容性好**: 保持原有的默认头像显示逻辑不变
2. **用户体验**: 详情页头像支持点击预览，带加载状态和错误处理
3. **响应式**: 头像大小适配不同显示场景
4. **错误处理**: 图片加载失败时显示友好的错误提示

## 测试验证

1. 打开用户管理页面，查看微信头像是否正常显示
2. 点击查看用户详情，确认微信头像显示正常
3. 点击详情页的微信头像，测试预览功能
4. 测试没有微信头像的用户，确认默认头像显示正常

## 注意事项

1. 此修改只影响微信头像的显示，其他头像（如形象照）保持原有逻辑
2. 修改后的头像显示方式与微信小程序中的显示效果一致
3. 如果微信更改防盗链策略，可能需要调整解决方案
