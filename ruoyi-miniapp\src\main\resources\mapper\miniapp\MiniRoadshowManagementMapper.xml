<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniRoadshowManagementMapper">
    
    <resultMap type="MiniRoadshowManagement" id="MiniRoadshowManagementResult">
        <result property="id"    column="id"    />
        <result property="uniqueCode"    column="unique_code"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMiniRoadshowManagementVo">
        select id, unique_code, cover_image, title, description, status, sort_order, create_by, create_time, update_by, update_time, remark from mini_roadshow_management
    </sql>

    <select id="selectMiniRoadshowManagementList" parameterType="MiniRoadshowManagement" resultMap="MiniRoadshowManagementResult">
        <include refid="selectMiniRoadshowManagementVo"/>
        <where>  
            <if test="uniqueCode != null  and uniqueCode != ''"> and unique_code = #{uniqueCode}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectMiniRoadshowManagementById" parameterType="Long" resultMap="MiniRoadshowManagementResult">
        <include refid="selectMiniRoadshowManagementVo"/>
        where id = #{id}
    </select>

    <select id="selectMiniRoadshowManagementByUniqueCode" parameterType="String" resultMap="MiniRoadshowManagementResult">
        <include refid="selectMiniRoadshowManagementVo"/>
        where unique_code = #{uniqueCode}
    </select>

    <select id="selectEnabledMiniRoadshowManagementList" resultMap="MiniRoadshowManagementResult">
        <include refid="selectMiniRoadshowManagementVo"/>
        where status = '0'
        order by sort_order asc, create_time desc
    </select>
        
    <insert id="insertMiniRoadshowManagement" parameterType="MiniRoadshowManagement" useGeneratedKeys="true" keyProperty="id">
        insert into mini_roadshow_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniqueCode != null and uniqueCode != ''">unique_code,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniqueCode != null and uniqueCode != ''">#{uniqueCode},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniRoadshowManagement" parameterType="MiniRoadshowManagement">
        update mini_roadshow_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiniRoadshowManagementById" parameterType="Long">
        delete from mini_roadshow_management where id = #{id}
    </delete>

    <delete id="deleteMiniRoadshowManagementByIds" parameterType="String">
        delete from mini_roadshow_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
