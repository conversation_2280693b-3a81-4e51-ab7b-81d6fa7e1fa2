<template>
  <div class="app-container">
    <h2>小程序端项目投资筛选测试</h2>

    <!-- 筛选条件 -->
    <el-form :model="filterParams" ref="filterForm" size="small" :inline="true">
      <el-form-item label="融资轮次">
        <el-select v-model="filterParams.financingRound" placeholder="请选择融资轮次" clearable>
          <el-option label="种子轮" value="种子轮" />
          <el-option label="天使轮" value="天使轮" />
          <el-option label="A轮融资" value="A轮融资" />
          <el-option label="B轮融资" value="B轮融资" />
          <el-option label="C轮融资" value="C轮融资" />
        </el-select>
      </el-form-item>

      <el-form-item label="所在地区">
        <el-select v-model="filterParams.region" placeholder="请选择地区" clearable>
          <el-option label="北京" value="北京" />
          <el-option label="上海" value="上海" />
          <el-option label="深圳" value="深圳" />
          <el-option label="天津" value="天津" />
          <el-option label="杭州" value="杭州" />
          <el-option label="广州" value="广州" />
          <el-option label="成都" value="成都" />
          <el-option label="重庆" value="重庆" />
        </el-select>
      </el-form-item>

      <el-form-item label="行业">
        <el-select v-model="filterParams.industryId" placeholder="请选择行业" clearable>
          <el-option label="航空航天" value="30" />
          <el-option label="硬科技" value="31" />
          <el-option label="人工智能" value="32" />
        </el-select>
      </el-form-item>

      <el-form-item label="关键字">
        <el-input
          v-model="filterParams.keyword"
          placeholder="请输入项目名称或关键字"
          clearable
          style="width: 200px;"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 结果展示 -->
    <el-divider>搜索结果</el-divider>

    <el-table v-loading="loading" :data="investmentList" border>
      <el-table-column label="项目名称" prop="projectName" width="150" />
      <el-table-column label="融资轮次" prop="financingRound" width="120">
        <template slot-scope="scope">
          <el-tag type="success">{{ scope.row.financingRound }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所属行业" prop="industryName" width="120" />
      <el-table-column label="所在地区" prop="region" width="100" />
      <el-table-column label="项目标签" prop="tags" width="200">
        <template slot-scope="scope">
          <el-tag
            v-for="(tag, index) in getTagArray(scope.row.tags)"
            :key="index"
            size="mini"
            style="margin-right: 5px;"
            type="info"
          >
            {{ tag }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目简介" prop="briefIntroduction" show-overflow-tooltip />
      <el-table-column label="联系人" prop="contactPerson" width="100" />
    </el-table>

    <div style="margin-top: 20px; text-align: center;">
      <span>共找到 {{ investmentList.length }} 个项目</span>
    </div>
  </div>
</template>

<script>
import { listEnabledInvestment } from "@/api/miniapp/investment";

export default {
  name: "MiniappInvestmentTest",
  data() {
    return {
      loading: false,
      investmentList: [],
      filterParams: {
        financingRound: '',
        region: '',
        industryId: '',
        keyword: ''
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询项目投资列表 */
    getList() {
      this.loading = true;

      // 构建查询参数，过滤空值
      const params = {};
      if (this.filterParams.financingRound) {
        params.financingRound = this.filterParams.financingRound;
      }
      if (this.filterParams.region) {
        params.region = this.filterParams.region;
      }
      if (this.filterParams.industryId) {
        params.industryId = this.filterParams.industryId;
      }
      if (this.filterParams.keyword) {
        params.keyword = this.filterParams.keyword;
      }

      listEnabledInvestment(params).then(response => {
        this.investmentList = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    /** 处理标签数组 */
    getTagArray(tags) {
      if (!tags) return [];
      return tags.split(',').filter(tag => tag.trim() !== '');
    },

    /** 搜索按钮操作 */
    handleSearch() {
      this.getList();
    },

    /** 重置按钮操作 */
    handleReset() {
      this.filterParams = {
        financingRound: '',
        region: '',
        industryId: '',
        keyword: ''
      };
      this.getList();
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
