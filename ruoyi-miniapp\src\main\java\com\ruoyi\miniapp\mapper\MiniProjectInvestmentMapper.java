package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniProjectInvestment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 项目投资Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface MiniProjectInvestmentMapper 
{
    /**
     * 查询项目投资
     * 
     * @param investmentId 项目投资主键
     * @return 项目投资
     */
    public MiniProjectInvestment selectMiniProjectInvestmentByInvestmentId(Long investmentId);

    /**
     * 查询项目投资列表
     * 
     * @param miniProjectInvestment 项目投资
     * @return 项目投资集合
     */
    public List<MiniProjectInvestment> selectMiniProjectInvestmentList(MiniProjectInvestment miniProjectInvestment);

    /**
     * 新增项目投资
     * 
     * @param miniProjectInvestment 项目投资
     * @return 结果
     */
    public int insertMiniProjectInvestment(MiniProjectInvestment miniProjectInvestment);

    /**
     * 修改项目投资
     * 
     * @param miniProjectInvestment 项目投资
     * @return 结果
     */
    public int updateMiniProjectInvestment(MiniProjectInvestment miniProjectInvestment);

    /**
     * 删除项目投资
     * 
     * @param investmentId 项目投资主键
     * @return 结果
     */
    public int deleteMiniProjectInvestmentByInvestmentId(Long investmentId);

    /**
     * 批量删除项目投资
     * 
     * @param investmentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniProjectInvestmentByInvestmentIds(Long[] investmentIds);

    /**
     * 查询启用的项目投资列表（小程序端调用）
     *
     * @return 项目投资集合
     */
    public List<MiniProjectInvestment> selectEnabledMiniProjectInvestmentList();

    /**
     * 查询启用的项目投资列表（带筛选条件）
     *
     * @param miniProjectInvestment 筛选条件
     * @param keyword 关键字
     * @return 项目投资集合
     */
    public List<MiniProjectInvestment> selectEnabledMiniProjectInvestmentListWithFilter(@Param("miniProjectInvestment") MiniProjectInvestment miniProjectInvestment,@Param("keyword") String keyword);

    /**
     * 查询推荐的项目投资列表
     *
     * @return 项目投资集合
     */
    public List<MiniProjectInvestment> selectRecommendedMiniProjectInvestmentList();

    /**
     * 增加项目投资浏览次数
     *
     * @param investmentId 项目投资主键
     * @return 结果
     */
    public int incrementViewCount(Long investmentId);

    /**
     * 根据行业ID查询项目投资列表
     *
     * @param industryId 行业ID
     * @return 项目投资集合
     */
    public List<MiniProjectInvestment> selectMiniProjectInvestmentByIndustryId(Long industryId);

    /**
     * 根据融资轮次查询项目投资列表
     *
     * @param financingRound 融资轮次
     * @return 项目投资集合
     */
    public List<MiniProjectInvestment> selectMiniProjectInvestmentByFinancingRound(String financingRound);

    /**
     * 根据地区查询项目投资列表
     *
     * @param region 地区
     * @return 项目投资集合
     */
    public List<MiniProjectInvestment> selectMiniProjectInvestmentByRegion(String region);
}
