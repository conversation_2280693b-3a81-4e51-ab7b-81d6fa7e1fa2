package com.ruoyi.miniapp.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniRoadshowManagementMapper;
import com.ruoyi.miniapp.domain.MiniRoadshowManagement;
import com.ruoyi.miniapp.service.IMiniRoadshowManagementService;

/**
 * 创赛路演管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class MiniRoadshowManagementServiceImpl implements IMiniRoadshowManagementService 
{
    @Autowired
    private MiniRoadshowManagementMapper miniRoadshowManagementMapper;

    /**
     * 查询创赛路演管理
     * 
     * @param id 创赛路演管理主键
     * @return 创赛路演管理
     */
    @Override
    public MiniRoadshowManagement selectMiniRoadshowManagementById(Long id)
    {
        return miniRoadshowManagementMapper.selectMiniRoadshowManagementById(id);
    }

    /**
     * 查询创赛路演管理列表
     * 
     * @param miniRoadshowManagement 创赛路演管理
     * @return 创赛路演管理
     */
    @Override
    public List<MiniRoadshowManagement> selectMiniRoadshowManagementList(MiniRoadshowManagement miniRoadshowManagement)
    {
        return miniRoadshowManagementMapper.selectMiniRoadshowManagementList(miniRoadshowManagement);
    }

    /**
     * 新增创赛路演管理
     * 
     * @param miniRoadshowManagement 创赛路演管理
     * @return 结果
     */
    @Override
    public int insertMiniRoadshowManagement(MiniRoadshowManagement miniRoadshowManagement)
    {
        miniRoadshowManagement.setCreateTime(DateUtils.getNowDate());
        return miniRoadshowManagementMapper.insertMiniRoadshowManagement(miniRoadshowManagement);
    }

    /**
     * 修改创赛路演管理
     * 
     * @param miniRoadshowManagement 创赛路演管理
     * @return 结果
     */
    @Override
    public int updateMiniRoadshowManagement(MiniRoadshowManagement miniRoadshowManagement)
    {
        miniRoadshowManagement.setUpdateTime(DateUtils.getNowDate());
        return miniRoadshowManagementMapper.updateMiniRoadshowManagement(miniRoadshowManagement);
    }

    /**
     * 批量删除创赛路演管理
     * 
     * @param ids 需要删除的创赛路演管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniRoadshowManagementByIds(Long[] ids)
    {
        return miniRoadshowManagementMapper.deleteMiniRoadshowManagementByIds(ids);
    }

    /**
     * 删除创赛路演管理信息
     * 
     * @param id 创赛路演管理主键
     * @return 结果
     */
    @Override
    public int deleteMiniRoadshowManagementById(Long id)
    {
        return miniRoadshowManagementMapper.deleteMiniRoadshowManagementById(id);
    }

    /**
     * 查询启用的创赛路演管理列表
     * 
     * @return 创赛路演管理集合
     */
    @Override
    public List<MiniRoadshowManagement> selectEnabledMiniRoadshowManagementList()
    {
        return miniRoadshowManagementMapper.selectEnabledMiniRoadshowManagementList();
    }

    /**
     * 根据唯一标识查询创赛路演管理
     * 
     * @param uniqueCode 唯一标识
     * @return 创赛路演管理
     */
    @Override
    public MiniRoadshowManagement selectMiniRoadshowManagementByUniqueCode(String uniqueCode)
    {
        return miniRoadshowManagementMapper.selectMiniRoadshowManagementByUniqueCode(uniqueCode);
    }

    /**
     * 校验唯一标识是否唯一
     * 
     * @param miniRoadshowManagement 创赛路演管理
     * @return 结果
     */
    @Override
    public boolean checkUniqueCodeUnique(MiniRoadshowManagement miniRoadshowManagement)
    {
        Long id = StringUtils.isNull(miniRoadshowManagement.getId()) ? -1L : miniRoadshowManagement.getId();
        MiniRoadshowManagement info = miniRoadshowManagementMapper.selectMiniRoadshowManagementByUniqueCode(miniRoadshowManagement.getUniqueCode());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return false;
        }
        return true;
    }
}
