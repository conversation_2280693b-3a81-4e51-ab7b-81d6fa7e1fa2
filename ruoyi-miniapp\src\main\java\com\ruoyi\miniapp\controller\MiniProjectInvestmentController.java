package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniProjectInvestment;
import com.ruoyi.miniapp.service.IMiniProjectInvestmentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 项目投资Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "项目投资管理")
@RestController
@RequestMapping("/miniapp/investment")
public class MiniProjectInvestmentController extends BaseController
{
    @Autowired
    private IMiniProjectInvestmentService miniProjectInvestmentService;

    /**
     * 查询项目投资列表
     */
    @ApiOperation("查询项目投资列表")
    @PreAuthorize("@ss.hasPermi('miniapp:investment:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniProjectInvestment miniProjectInvestment)
    {
        startPage();
        List<MiniProjectInvestment> list = miniProjectInvestmentService.selectMiniProjectInvestmentList(miniProjectInvestment);
        return getDataTable(list);
    }

    /**
     * 导出项目投资列表
     */
    @ApiOperation("导出项目投资列表")
    @PreAuthorize("@ss.hasPermi('miniapp:investment:export')")
    @Log(title = "项目投资", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniProjectInvestment miniProjectInvestment)
    {
        List<MiniProjectInvestment> list = miniProjectInvestmentService.selectMiniProjectInvestmentList(miniProjectInvestment);
        ExcelUtil<MiniProjectInvestment> util = new ExcelUtil<MiniProjectInvestment>(MiniProjectInvestment.class);
        util.exportExcel(response, list, "项目投资数据");
    }

    /**
     * 获取项目投资详细信息
     */
    @ApiOperation("获取项目投资详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:investment:query')")
    @GetMapping(value = "/{investmentId}")
    public AjaxResult getInfo(@ApiParam(name = "investmentId", value = "项目投资ID", required = true) @PathVariable("investmentId") Long investmentId)
    {
        return success(miniProjectInvestmentService.selectMiniProjectInvestmentByInvestmentId(investmentId));
    }

    /**
     * 新增项目投资
     */
    @ApiOperation("新增项目投资")
    @PreAuthorize("@ss.hasPermi('miniapp:investment:add')")
    @Log(title = "项目投资", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(name = "miniProjectInvestment", value = "项目投资信息", required = true) @RequestBody MiniProjectInvestment miniProjectInvestment)
    {
        return toAjax(miniProjectInvestmentService.insertMiniProjectInvestment(miniProjectInvestment));
    }

    /**
     * 修改项目投资
     */
    @ApiOperation("修改项目投资")
    @PreAuthorize("@ss.hasPermi('miniapp:investment:edit')")
    @Log(title = "项目投资", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam(name = "miniProjectInvestment", value = "项目投资信息", required = true) @RequestBody MiniProjectInvestment miniProjectInvestment)
    {
        return toAjax(miniProjectInvestmentService.updateMiniProjectInvestment(miniProjectInvestment));
    }

    /**
     * 删除项目投资
     */
    @ApiOperation("删除项目投资")
    @PreAuthorize("@ss.hasPermi('miniapp:investment:remove')")
    @Log(title = "项目投资", businessType = BusinessType.DELETE)
	@DeleteMapping("/{investmentIds}")
    public AjaxResult remove(@ApiParam(name = "investmentIds", value = "项目投资ID数组", required = true) @PathVariable Long[] investmentIds)
    {
        return toAjax(miniProjectInvestmentService.deleteMiniProjectInvestmentByInvestmentIds(investmentIds));
    }

    /**
     * 查询启用的项目投资列表（小程序端调用）
     */
    @ApiOperation("查询启用的项目投资列表")
    @GetMapping("/enabled")
    public AjaxResult getEnabledList(
            @ApiParam(name = "financingRound", value = "融资轮次") String financingRound,
            @ApiParam(name = "region", value = "所在地区") String region,
            @ApiParam(name = "industryId", value = "行业ID") Long industryId,
            @ApiParam(name = "keyword", value = "关键字（项目名称或简介或标签）") String keyword)
    {
        MiniProjectInvestment queryParam = new MiniProjectInvestment();
        queryParam.setFinancingRound(financingRound);
        queryParam.setRegion(region);
        queryParam.setIndustryId(industryId);
        queryParam.setProjectName(keyword); // 用于项目名称搜索
        queryParam.setBriefIntroduction(keyword); // 用于简介搜索
        queryParam.setStatus("0"); // 只查询启用状态

        List<MiniProjectInvestment> list = miniProjectInvestmentService.selectEnabledMiniProjectInvestmentListWithFilter(queryParam, keyword);
        return success(list);
    }

    /**
     * 查询推荐的项目投资列表
     */
    @ApiOperation("查询推荐的项目投资列表")
    @GetMapping("/recommended")
    public AjaxResult getRecommendedList()
    {
        List<MiniProjectInvestment> list = miniProjectInvestmentService.selectRecommendedMiniProjectInvestmentList();
        return success(list);
    }

    /**
     * 增加项目投资浏览次数
     */
    @ApiOperation("增加项目投资浏览次数")
    @PostMapping("/view/{investmentId}")
    public AjaxResult incrementViewCount(@ApiParam(name = "investmentId", value = "项目投资ID", required = true) @PathVariable Long investmentId)
    {
        return toAjax(miniProjectInvestmentService.incrementViewCount(investmentId));
    }

    /**
     * 根据行业ID查询项目投资列表
     */
    @ApiOperation("根据行业ID查询项目投资列表")
    @GetMapping("/industry/{industryId}")
    public AjaxResult getByIndustryId(@ApiParam(name = "industryId", value = "行业ID", required = true) @PathVariable Long industryId)
    {
        List<MiniProjectInvestment> list = miniProjectInvestmentService.selectMiniProjectInvestmentByIndustryId(industryId);
        return success(list);
    }

    /**
     * 根据融资轮次查询项目投资列表
     */
    @ApiOperation("根据融资轮次查询项目投资列表")
    @GetMapping("/financing/{financingRound}")
    public AjaxResult getByFinancingRound(@ApiParam(name = "financingRound", value = "融资轮次", required = true) @PathVariable String financingRound)
    {
        List<MiniProjectInvestment> list = miniProjectInvestmentService.selectMiniProjectInvestmentByFinancingRound(financingRound);
        return success(list);
    }

    /**
     * 根据地区查询项目投资列表
     */
    @ApiOperation("根据地区查询项目投资列表")
    @GetMapping("/region/{region}")
    public AjaxResult getByRegion(@ApiParam(name = "region", value = "地区名称", required = true) @PathVariable String region)
    {
        List<MiniProjectInvestment> list = miniProjectInvestmentService.selectMiniProjectInvestmentByRegion(region);
        return success(list);
    }
}
