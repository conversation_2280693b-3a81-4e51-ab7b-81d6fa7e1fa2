<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模块名称" prop="moduleName">
        <el-input
          v-model="queryParams.moduleName"
          placeholder="请输入模块名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模块代码" prop="moduleCode">
        <el-input
          v-model="queryParams.moduleCode"
          placeholder="请输入模块代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="链接类型" prop="linkType">
        <el-select v-model="queryParams.linkType" placeholder="请选择链接类型" clearable>
          <el-option label="内部页面" value="1" />
          <el-option label="外部链接" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['config:homemodule:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['config:homemodule:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['config:homemodule:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['config:homemodule:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="homeModuleList" @selection-change="handleSelectionChange" style="width: 100%">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="70" />
      <el-table-column label="模块图标" align="center" prop="moduleIcon" width="90">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.moduleIcon" :src="scope.row.moduleIcon" :width="50" :height="50"/>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="模块名称" align="center" prop="moduleName" width="120" show-overflow-tooltip />
      <el-table-column label="模块代码" align="center" prop="moduleCode" width="150" show-overflow-tooltip />
      <el-table-column label="链接类型" align="center" prop="linkType" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.linkType === '1'" type="primary" size="mini">内部页面</el-tag>
          <el-tag v-else-if="scope.row.linkType === '2'" type="warning" size="mini">外部链接</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="跳转地址" align="center" show-overflow-tooltip min-width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.linkType === '1'">{{ scope.row.moduleUrl || '-' }}</span>
          <span v-else-if="scope.row.linkType === '2'">{{ scope.row.externalUrl || '-' }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" width="110">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.sortOrder"
            :min="0"
            :max="9999"
            size="mini"
            controls-position="right"
            @change="handleSortOrderChange(scope.row)"
            style="width: 90px;"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="90">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '0'" type="success" size="mini">正常</el-tag>
          <el-tag v-else type="danger" size="mini">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="130" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['config:homemodule:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['config:homemodule:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改小程序首页功能模块对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="模块名称" prop="moduleName">
          <el-input v-model="form.moduleName" placeholder="请输入模块名称" maxlength="100" />
        </el-form-item>
        <el-form-item label="模块代码" prop="moduleCode">
          <el-input
            v-model="form.moduleCode"
            placeholder="请输入模块代码"
            maxlength="50"
            :disabled="form.id != null"
          />
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            模块代码用于系统内部识别，必须唯一，创建后不可修改
          </div>
        </el-form-item>
        <el-form-item label="模块图标" prop="moduleIcon">
          <image-upload v-model="form.moduleIcon"/>
        </el-form-item>
        <el-form-item label="链接类型" prop="linkType">
          <el-radio-group v-model="form.linkType" @change="handleLinkTypeChange">
            <el-radio label="1">内部页面</el-radio>
            <el-radio label="2">外部链接</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.linkType === '1'" label="内部链接" prop="moduleUrl">
          <el-input v-model="form.moduleUrl" placeholder="请输入小程序页面路径，如：/pages/home/<USER>" />
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            小程序内部页面路径，以 / 开头
          </div>
        </el-form-item>
        <el-form-item v-if="form.linkType === '2'" label="外部链接" prop="externalUrl">
          <el-input v-model="form.externalUrl" placeholder="请输入完整的URL地址，如：https://www.example.com" />
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            外部网站链接，需要包含 http:// 或 https://
          </div>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" :max="9999" />
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            数字越小越靠前显示
          </div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHomeModule, getHomeModule, delHomeModule, addHomeModule, updateHomeModule, checkModuleCodeUnique } from "@/api/miniapp/homemodule";

export default {
  name: "HomeModule",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小程序首页功能模块表格数据
      homeModuleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        moduleName: null,
        moduleCode: null,
        linkType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 排序防抖定时器
      sortOrderTimer: null,
      // 表单校验
      rules: {
        moduleName: [
          { required: true, message: "模块名称不能为空", trigger: "blur" },
          { min: 1, max: 100, message: "模块名称长度必须介于 1 和 100 之间", trigger: "blur" }
        ],
        moduleCode: [
          { required: true, message: "模块代码不能为空", trigger: "blur" },
          { min: 1, max: 50, message: "模块代码长度必须介于 1 和 50 之间", trigger: "blur" },
          { validator: this.validateModuleCode, trigger: "blur" }
        ],
        linkType: [
          { required: true, message: "链接类型不能为空", trigger: "change" }
        ],
        moduleUrl: [
          { validator: this.validateModuleUrl, trigger: "blur" }
        ],
        externalUrl: [
          { validator: this.validateExternalUrl, trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    // 清理定时器
    if (this.sortOrderTimer) {
      clearTimeout(this.sortOrderTimer);
    }
  },
  methods: {
    /** 查询小程序首页功能模块列表 */
    getList() {
      this.loading = true;
      listHomeModule(this.queryParams).then(response => {
        this.homeModuleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        moduleName: null,
        moduleIcon: null,
        moduleCode: null,
        moduleUrl: null,
        linkType: "1",
        externalUrl: null,
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加小程序首页功能模块";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getHomeModule(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改小程序首页功能模块";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateHomeModule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHomeModule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除小程序首页功能模块编号为"' + ids + '"的数据项？').then(function() {
        return delHomeModule(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/homemodule/export', {
        ...this.queryParams
      }, `homemodule_${new Date().getTime()}.xlsx`)
    },
    /** 链接类型改变处理 */
    handleLinkTypeChange(value) {
      // 清空相关字段
      if (value === '1') {
        this.form.externalUrl = null;
      } else if (value === '2') {
        this.form.moduleUrl = null;
      }
    },
    /** 排序值改变处理 */
    handleSortOrderChange(row) {
      // 防抖处理，避免频繁请求
      if (this.sortOrderTimer) {
        clearTimeout(this.sortOrderTimer);
      }
      this.sortOrderTimer = setTimeout(() => {
        const updateData = {
          id: row.id,
          moduleName: row.moduleName,
          moduleIcon: row.moduleIcon,
          moduleCode: row.moduleCode,
          moduleUrl: row.moduleUrl,
          linkType: row.linkType,
          externalUrl: row.externalUrl,
          sortOrder: row.sortOrder,
          status: row.status,
          remark: row.remark
        };
        updateHomeModule(updateData).then(() => {
          this.$modal.msgSuccess("排序更新成功");
          this.getList(); // 重新加载列表以显示最新排序
        }).catch(() => {
          this.$modal.msgError("排序更新失败");
          this.getList(); // 失败时也重新加载，恢复原始数据
        });
      }, 800); // 800ms防抖
    },
    /** 模块代码校验 */
    validateModuleCode(rule, value, callback) {
      if (value) {
        const data = {
          id: this.form.id,
          moduleCode: value
        };
        checkModuleCodeUnique(data).then(response => {
          if (response.data) {
            callback();
          } else {
            callback(new Error("模块代码已存在"));
          }
        }).catch(() => {
          callback(new Error("校验失败"));
        });
      } else {
        callback();
      }
    },
    /** 内部链接校验 */
    validateModuleUrl(rule, value, callback) {
      if (this.form.linkType === '1') {
        if (!value) {
          callback(new Error("内部链接不能为空"));
        } else if (!value.startsWith('/')) {
          callback(new Error("内部链接必须以 / 开头"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    /** 外部链接校验 */
    validateExternalUrl(rule, value, callback) {
      if (this.form.linkType === '2') {
        if (!value) {
          callback(new Error("外部链接不能为空"));
        } else if (!/^https?:\/\/.+/.test(value)) {
          callback(new Error("外部链接必须以 http:// 或 https:// 开头"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    }
  }
};
</script>
