package com.ruoyi.miniapp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.domain.MiniHaitangProjectRegistration;
import com.ruoyi.miniapp.service.IMiniHaitangProjectRegistrationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 天大海棠杯项目报名记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Api(tags = "天大海棠杯项目报名记录管理")
@RestController
@RequestMapping("/miniapp/haitang/registration")
public class MiniHaitangProjectRegistrationController extends BaseController
{
    @Autowired
    private IMiniHaitangProjectRegistrationService miniHaitangProjectRegistrationService;

    /**
     * 查询天大海棠杯项目报名记录列表
     */
    @ApiOperation("查询天大海棠杯项目报名记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:registration:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        startPage();
        List<MiniHaitangProjectRegistration> list = miniHaitangProjectRegistrationService.selectMiniHaitangProjectRegistrationListWithConfig(miniHaitangProjectRegistration);
        return getDataTable(list);
    }

    /**
     * 导出天大海棠杯项目报名记录列表
     */
    @ApiOperation("导出天大海棠杯项目报名记录列表")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:registration:export')")
    @Log(title = "天大海棠杯项目报名记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        List<MiniHaitangProjectRegistration> list = miniHaitangProjectRegistrationService.selectMiniHaitangProjectRegistrationListWithConfig(miniHaitangProjectRegistration);
        ExcelUtil<MiniHaitangProjectRegistration> util = new ExcelUtil<MiniHaitangProjectRegistration>(MiniHaitangProjectRegistration.class);
        util.exportExcel(response, list, "天大海棠杯项目报名记录数据");
    }

    /**
     * 获取天大海棠杯项目报名记录详细信息
     */
    @ApiOperation("获取天大海棠杯项目报名记录详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:registration:query')")
    @GetMapping(value = "/{registrationId}")
    public AjaxResult getInfo(@ApiParam("报名ID") @PathVariable("registrationId") Long registrationId)
    {
        return AjaxResult.success(miniHaitangProjectRegistrationService.selectMiniHaitangProjectRegistrationWithConfigByRegistrationId(registrationId));
    }

    /**
     * 新增天大海棠杯项目报名记录
     */
    @ApiOperation("新增天大海棠杯项目报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:registration:add')")
    @Log(title = "天大海棠杯项目报名记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        miniHaitangProjectRegistration.setCreateBy(getUsername());
        return toAjax(miniHaitangProjectRegistrationService.insertMiniHaitangProjectRegistration(miniHaitangProjectRegistration));
    }

    /**
     * 修改天大海棠杯项目报名记录
     */
    @ApiOperation("修改天大海棠杯项目报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:registration:edit')")
    @Log(title = "天大海棠杯项目报名记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        miniHaitangProjectRegistration.setUpdateBy(getUsername());
        return toAjax(miniHaitangProjectRegistrationService.updateMiniHaitangProjectRegistration(miniHaitangProjectRegistration));
    }

    /**
     * 删除天大海棠杯项目报名记录
     */
    @ApiOperation("删除天大海棠杯项目报名记录")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:registration:remove')")
    @Log(title = "天大海棠杯项目报名记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{registrationIds}")
    public AjaxResult remove(@ApiParam("报名ID数组") @PathVariable Long[] registrationIds)
    {
        return toAjax(miniHaitangProjectRegistrationService.deleteMiniHaitangProjectRegistrationByRegistrationIds(registrationIds));
    }



    // ==================== 小程序端接口 ====================

    /**
     * 提交项目报名（小程序端）
     */
    @ApiOperation("提交项目报名")
    @PostMapping("/app/submit")
    public AjaxResult submit(@RequestBody MiniHaitangProjectRegistration miniHaitangProjectRegistration)
    {
        miniHaitangProjectRegistration.setCreateBy("miniapp_user");
        return toAjax(miniHaitangProjectRegistrationService.insertMiniHaitangProjectRegistration(miniHaitangProjectRegistration));
    }

    /**
     * 查询用户报名记录（小程序端）
     */
    @ApiOperation("查询用户报名记录")
    @GetMapping("/app/myRegistrations")
    public AjaxResult getMyRegistrations(Long userId)
    {
        MiniHaitangProjectRegistration query = new MiniHaitangProjectRegistration();
        query.setUserId(userId);
        List<MiniHaitangProjectRegistration> list = miniHaitangProjectRegistrationService.selectMiniHaitangProjectRegistrationListWithConfig(query);
        return AjaxResult.success(list);
    }
}
