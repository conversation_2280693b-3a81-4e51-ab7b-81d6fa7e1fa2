package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniRoadshowManagement;
import org.apache.ibatis.annotations.Mapper;

/**
 * 创赛路演管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Mapper
public interface MiniRoadshowManagementMapper 
{
    /**
     * 查询创赛路演管理
     * 
     * @param id 创赛路演管理主键
     * @return 创赛路演管理
     */
    public MiniRoadshowManagement selectMiniRoadshowManagementById(Long id);

    /**
     * 查询创赛路演管理列表
     * 
     * @param miniRoadshowManagement 创赛路演管理
     * @return 创赛路演管理集合
     */
    public List<MiniRoadshowManagement> selectMiniRoadshowManagementList(MiniRoadshowManagement miniRoadshowManagement);

    /**
     * 新增创赛路演管理
     * 
     * @param miniRoadshowManagement 创赛路演管理
     * @return 结果
     */
    public int insertMiniRoadshowManagement(MiniRoadshowManagement miniRoadshowManagement);

    /**
     * 修改创赛路演管理
     * 
     * @param miniRoadshowManagement 创赛路演管理
     * @return 结果
     */
    public int updateMiniRoadshowManagement(MiniRoadshowManagement miniRoadshowManagement);

    /**
     * 删除创赛路演管理
     * 
     * @param id 创赛路演管理主键
     * @return 结果
     */
    public int deleteMiniRoadshowManagementById(Long id);

    /**
     * 批量删除创赛路演管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniRoadshowManagementByIds(Long[] ids);

    /**
     * 查询启用的创赛路演管理列表
     * 
     * @return 创赛路演管理集合
     */
    public List<MiniRoadshowManagement> selectEnabledMiniRoadshowManagementList();

    /**
     * 根据唯一标识查询创赛路演管理
     * 
     * @param uniqueCode 唯一标识
     * @return 创赛路演管理
     */
    public MiniRoadshowManagement selectMiniRoadshowManagementByUniqueCode(String uniqueCode);
}
