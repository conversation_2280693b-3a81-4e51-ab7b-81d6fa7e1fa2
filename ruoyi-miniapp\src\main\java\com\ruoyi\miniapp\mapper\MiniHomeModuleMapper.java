package com.ruoyi.miniapp.mapper;

import java.util.List;
import com.ruoyi.miniapp.domain.MiniHomeModule;
import org.apache.ibatis.annotations.Mapper;

/**
 * 小程序首页功能模块Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Mapper
public interface MiniHomeModuleMapper 
{
    /**
     * 查询小程序首页功能模块
     * 
     * @param id 小程序首页功能模块主键
     * @return 小程序首页功能模块
     */
    public MiniHomeModule selectMiniHomeModuleById(Long id);

    /**
     * 查询小程序首页功能模块列表
     * 
     * @param miniHomeModule 小程序首页功能模块
     * @return 小程序首页功能模块集合
     */
    public List<MiniHomeModule> selectMiniHomeModuleList(MiniHomeModule miniHomeModule);

    /**
     * 新增小程序首页功能模块
     * 
     * @param miniHomeModule 小程序首页功能模块
     * @return 结果
     */
    public int insertMiniHomeModule(MiniHomeModule miniHomeModule);

    /**
     * 修改小程序首页功能模块
     * 
     * @param miniHomeModule 小程序首页功能模块
     * @return 结果
     */
    public int updateMiniHomeModule(MiniHomeModule miniHomeModule);

    /**
     * 删除小程序首页功能模块
     * 
     * @param id 小程序首页功能模块主键
     * @return 结果
     */
    public int deleteMiniHomeModuleById(Long id);

    /**
     * 批量删除小程序首页功能模块
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiniHomeModuleByIds(Long[] ids);

    /**
     * 根据模块代码查询小程序首页功能模块
     *
     * @param moduleCode 模块代码
     * @return 小程序首页功能模块
     */
    public MiniHomeModule selectMiniHomeModuleByModuleCode(String moduleCode);

    /**
     * 查询正常状态的模块列表（小程序端使用）
     *
     * @return 小程序首页功能模块集合
     */
    public List<MiniHomeModule> selectActiveModuleList();
}
